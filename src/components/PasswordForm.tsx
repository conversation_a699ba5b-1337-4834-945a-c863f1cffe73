import React from 'react';
import { Form, Input, Checkbox, type FormInstance } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';
import { EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import PasswordRequirements from '@/components/PasswordRequirements';

export interface PasswordFormData {
  password: string;
  confirm: string;
}

export interface PasswordFormProps {
  /** Form 实例，用于外部控制表单 */
  form?: FormInstance;
  /** 密码值，用于密码要求显示 */
  password?: string;
  /** 密码变化回调 */
  onPasswordChange?: (password: string) => void;
  /** 是否显示服务条款复选框 */
  showTermsCheckbox?: boolean;
  /** 服务条款是否已同意 */
  agreeTerms?: boolean;
  /** 服务条款变化回调 */
  onAgreeTermsChange?: (agreed: boolean) => void;
  /** 是否显示密码要求 */
  showPasswordRequirements?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 密码字段的额外验证规则 */
  passwordRules?: any[];
  /** 确认密码字段的额外验证规则 */
  confirmPasswordRules?: any[];
}

const PasswordForm: React.FC<PasswordFormProps> = ({
  form,
  password = '',
  onPasswordChange,
  showTermsCheckbox = true,
  agreeTerms = false,
  onAgreeTermsChange,
  showPasswordRequirements = true,
  className = '',
  passwordRules = [],
  confirmPasswordRules = [],
}) => {
  const { t } = useLanguage();

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    onPasswordChange?.(value);
  };

  const handleTermsChange = (e: any) => {
    onAgreeTermsChange?.(e.target.checked);
  };

  // 默认密码验证规则
  const defaultPasswordRules = [
    {
      required: true,
      message: t('auth.register.step4.form.passwordRequired'),
    },
    {
      min: 8,
      message: t('auth.register.step4.form.passwordRequirements.length'),
    },
    ...passwordRules,
  ];

  // 默认确认密码验证规则
  const defaultConfirmPasswordRules = [
    {
      required: true,
      message: t('auth.register.step4.form.confirmPasswordRequired'),
    },
    ({ getFieldValue }: any) => ({
      validator(_: any, value: any) {
        if (!value || getFieldValue('password') === value) {
          return Promise.resolve();
        }
        return Promise.reject(
          new Error(t('auth.register.step4.form.passwordMismatch'))
        );
      },
    }),
    ...confirmPasswordRules,
  ];

  return (
    <div className={className}>
      <Form.Item
        label={
          <span className="text-base text-gray-700 font-medium">
            {t('auth.register.step4.form.password')}:
          </span>
        }
        name="password"
        rules={defaultPasswordRules}
        className="mb-4"
      >
        <Input.Password
          placeholder={t('auth.register.step4.form.passwordPlaceholder')}
          onChange={handlePasswordChange}
          iconRender={visible =>
            visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
          }
          className="placeholder:font-inter h-[54px] rounded-md border-none bg-form-item px-5 text-black placeholder:(text-[12px] text-black/25)"
        />
      </Form.Item>

      {/* 密码要求列表 */}
      {showPasswordRequirements && <PasswordRequirements password={password} />}

      <Form.Item
        name="confirm"
        label={
          <span className="text-base text-gray-700 font-medium">
            {t('auth.register.step4.form.confirmPassword')}:
          </span>
        }
        dependencies={['password']}
        rules={defaultConfirmPasswordRules}
        className="mb-6"
      >
        <Input.Password
          placeholder={t('auth.register.step4.form.confirmPasswordPlaceholder')}
          iconRender={visible =>
            visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
          }
          className="placeholder:font-inter h-[54px] rounded-md border-none bg-form-item px-5 text-black placeholder:(text-[12px] text-black/25)"
        />
      </Form.Item>

      {/* 服务条款复选框 */}
      {showTermsCheckbox && (
        <div className="mb-6">
          <Checkbox
            checked={agreeTerms}
            onChange={handleTermsChange}
            className="text-sm text-label"
          >
            {t('auth.register.step4.form.agreeTerms')}{' '}
            <a href="#" className="text-primary hover:text-primary">
              {t('auth.register.step4.form.termsOfService')}
            </a>{' '}
            {t('auth.register.step4.form.and')}{' '}
            <a href="#" className="text-primary hover:text-primary">
              {t('auth.register.step4.form.privacyPolicy')}
            </a>
          </Checkbox>
        </div>
      )}
    </div>
  );
};

export default PasswordForm;
