# PasswordForm 组件

一个可复用的密码设置表单组件，包含密码输入、确认密码、密码要求显示和服务条款复选框。

## 功能特性

- 密码输入字段（带显示/隐藏切换）
- 确认密码字段（自动验证密码匹配）
- 密码要求实时显示
- 服务条款复选框
- 完全可配置的验证规则
- 支持自定义样式

## 使用方法

### 基本用法

```tsx
import React, { useState } from 'react';
import { Form } from 'antd';
import PasswordForm, { PasswordFormData } from '@/components/PasswordForm';

const MyComponent = () => {
  const [form] = Form.useForm();
  const [password, setPassword] = useState('');
  const [agreeTerms, setAgreeTerms] = useState(false);

  const handleSubmit = (values: PasswordFormData) => {
    console.log('密码数据:', values);
  };

  return (
    <Form form={form} onFinish={handleSubmit}>
      <PasswordForm
        form={form}
        password={password}
        onPasswordChange={setPassword}
        agreeTerms={agreeTerms}
        onAgreeTermsChange={setAgreeTerms}
      />
      <button type="submit">提交</button>
    </Form>
  );
};
```

### 自定义配置

```tsx
<PasswordForm
  form={form}
  password={password}
  onPasswordChange={setPassword}
  showTermsCheckbox={false}  // 隐藏服务条款
  showPasswordRequirements={false}  // 隐藏密码要求
  passwordRules={[
    { min: 12, message: '密码至少12位' }  // 自定义验证规则
  ]}
  className="my-custom-class"
/>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| form | FormInstance | - | Ant Design Form 实例 |
| password | string | '' | 当前密码值 |
| onPasswordChange | (password: string) => void | - | 密码变化回调 |
| showTermsCheckbox | boolean | true | 是否显示服务条款复选框 |
| agreeTerms | boolean | false | 服务条款是否已同意 |
| onAgreeTermsChange | (agreed: boolean) => void | - | 服务条款变化回调 |
| showPasswordRequirements | boolean | true | 是否显示密码要求 |
| className | string | '' | 自定义样式类名 |
| passwordRules | any[] | [] | 密码字段额外验证规则 |
| confirmPasswordRules | any[] | [] | 确认密码字段额外验证规则 |

## 数据类型

```tsx
interface PasswordFormData {
  password: string;
  confirm: string;
}
```

## 注意事项

1. 组件必须在 Ant Design Form 组件内使用
2. 密码要求组件依赖 `PasswordRequirements` 组件
3. 所有文本使用国际化 hook `useLanguage`
4. 组件样式使用 UnoCSS 类名
