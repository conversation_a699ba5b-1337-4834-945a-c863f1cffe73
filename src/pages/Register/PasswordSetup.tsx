import React, { useMemo } from 'react';
import { Form, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '@/hooks/useLanguage';
import { useRegisterStore } from '@/store/registerStore';
import { useAuthStore } from '@/store';
import { ApiService } from '@/services';
import PasswordForm, { type PasswordFormData } from '@/components/PasswordForm';
import FormButton from './coms/FormButton';
import { getTimezone } from '@/components/CountryRegionSelector/timezoneMap';

const PasswordSetup: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { formData, clearFormData } = useRegisterStore();
  const { login } = useAuthStore();
  const [form] = Form.useForm();
  const [loading, setLoading] = React.useState(false);
  const [password, setPassword] = React.useState('');
  const [agreeTerms, setAgreeTerms] = React.useState(false);

  const onPasswordChange = (value: string) => {
    setPassword(value);
  };

  const onFinish = async (values: PasswordFormData) => {
    if (!agreeTerms) {
      message.error(t('auth.register.step4.form.agreeTermsRequired'));
      return;
    }
    console.log('提交了');

    const finalData = {
      ...formData,
      password: values.password,
    };
    console.log('finalData----', finalData);
    const timezone = getTimezone(
      formData.countryCode || '',
      formData.region || ''
    );
    console.log('timezone----', timezone);
    setLoading(true);
    try {
      // const response = await ApiService.post('/auth/register', finalData);
      // if (response.data.token) {
      //   // 注册成功后自动登录
      //   login();
      //   message.success(t('auth.register.success'));
      //   clearFormData(); // 清空注册数据
      //   navigate('/');
      // } else {
      //   message.error(response.message || t('auth.register.error'));
      // }
    } catch (error: any) {
      message.error(error?.response?.data?.message || t('auth.register.error'));
    } finally {
      setLoading(false);
    }
  };

  const disabledButton = useMemo(() => {
    return !agreeTerms || !password.trim();
  }, [agreeTerms, password]);

  // 检查是否有前面步骤的数据，如果没有则重定向
  React.useEffect(() => {
    if (!formData.email) {
      message.warning(
        t('auth.register.step4.messages.emailVerificationRequired')
      );
      navigate('/register/email');
    } else if (!formData.alias) {
      message.warning(t('auth.register.step4.messages.aliasRequired'));
      navigate('/register/alias');
    } else if (!formData.firstName || !formData.lastName) {
      message.warning(t('auth.register.step4.messages.personalInfoRequired'));
      navigate('/register/personal-info');
    }
  }, [formData, navigate, t]);

  return (
    <>
      <h1 className="font-arial mb-13 text-center text-[32px] text-[#ff5e13] font-bold">
        {t('auth.register.step4.title')}
      </h1>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        autoComplete="off"
        size="large"
      >
        <PasswordForm
          form={form}
          password={password}
          onPasswordChange={onPasswordChange}
          agreeTerms={agreeTerms}
          onAgreeTermsChange={setAgreeTerms}
        />

        <FormButton loading={loading} disabled={disabledButton}>
          {t('auth.register.step4.form.signUp')}
        </FormButton>
      </Form>
    </>
  );
};

export default PasswordSetup;
